import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { FactureVente } from '../models/facture-vente';


@Injectable({
  providedIn: 'root'
})
export class FactureVenteService {
  private apiUrl = 'http://localhost:5251/api/factures-vente';

  constructor(private http: HttpClient) {}

  getFacturesVente(): Observable<FactureVente[]> {
    return this.http.get<FactureVente[]>(this.apiUrl);
  }

  getFactureVente(id: string): Observable<FactureVente> {
    return this.http.get<FactureVente>(`${this.apiUrl}/${id}`);
  }

  postFactureVente(facture: FactureVente): Observable<FactureVente> {
    return this.http.post<FactureVente>(this.apiUrl, facture);
  }

  putFactureVente(id: string, facture: FactureVente): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, facture);
  }

  deleteFactureVente(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
}