import { Applicationuser } from "./applicationuser";
import { StatutFacture } from "./facture-vente";
import { Fournisseur } from "./fournisseur";
import { Societe } from "./societe";

export interface FactureAchat {
  id: string;
  numero: string;
  montant: number;
  date: Date;
  dateEcheance?: Date;
  statut: StatutFacture;
  dateEnvoi?: Date;
  datePaiement?: Date;
  notesInternes?: string;
  matriculeFiscaleSociete: string;
  societeId: string;
  societe?: Societe;
  utilisateurCreationId?: string;
  utilisateurCreation?: Applicationuser;
  dateCreation: Date;
  dateModification?: Date;
  fournisseurId: string;
  fournisseur?: Fournisseur;
  cheminFichier?: string;
}