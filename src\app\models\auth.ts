import { Applicationuser } from "./applicationuser";

export interface UserLoginDto {
  email: string;
  motDePasse: string;
}

export interface UserRegisterDto {
  nom: string;
  email: string;
  motDePasse: string;
  societeNom: string;
  societeAdresse: string;
  matriculeFiscale: string;
  societeEmail?: string;
  societeTelephone?: string;
  signature?: string;
  cachet?: string;
}

export interface AuthResponse {
  token: string;
  message: string;
  user: Applicationuser;
}