import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { FactureAchat } from '../models/facture-achat';
@Injectable({
  providedIn: 'root'
})
export class FactureAchatService {
  private apiUrl = 'http://localhost:5251/api/factures-achat';

  constructor(private http: HttpClient) {}

  getFacturesAchat(): Observable<FactureAchat[]> {
    return this.http.get<FactureAchat[]>(this.apiUrl);
  }

  getFactureAchat(id: string): Observable<FactureAchat> {
    return this.http.get<FactureAchat>(`${this.apiUrl}/${id}`);
  }

  postFactureAchat(formData: FormData): Observable<FactureAchat> {
    return this.http.post<FactureAchat>(this.apiUrl, formData);
  }

  putFactureAchat(id: string, facture: FactureAchat): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, facture);
  }

  deleteFactureAchat(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
}