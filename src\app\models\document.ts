import { Applicationuser } from "./applicationuser";
import { Societe } from "./societe";

export enum StatutDocument {
  EnAttente = 0,
  Approuve = 1,
  Rejete = 2
}

export enum TypeDemandeDocument {
  AttestationTravail = 0,
  AttestationStage = 1,
  CertificatFormation = 2,
  Autre = 3
}

export interface Document {
  id: string;
  titre: string;
  contenu: string;
  dateCreation: Date;
  dateModification?: Date;
  statut: StatutDocument;
  typeDemande: TypeDemandeDocument;
  typeDocumentId: string;
  typeDocument?: TypeDocument;
  societeId: string;
  societe?: Societe;
  utilisateurDemandeurId?: string;
  utilisateurDemandeur?: Applicationuser;
  utilisateurApprobateurId?: string;
  utilisateurApprobateur?: Applicationuser;
}

export interface TypeDocument {
  id: string;
  nom: string;
  description?: string;
  societeId: string;
  societe?: Societe;
  documents?: Document[];
}