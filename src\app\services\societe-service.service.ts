import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Societe } from '../models/societe';

@Injectable({
  providedIn: 'root'
})
export class SocieteService {
  private apiUrl = 'http://localhost:5251/api/Societes';

  constructor(private http: HttpClient) {}

  getSociete(): Observable<Societe> {
    return this.http.get<Societe>(this.apiUrl);
  }

  putSociete(societe: Societe): Observable<any> {
    return this.http.put(this.apiUrl, societe);
  }
}