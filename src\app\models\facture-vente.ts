import { Applicationuser } from "./applicationuser";
import { Client } from "./client";
import { Societe } from "./societe";

export enum StatutFacture {
  Brouillon = 0,
  Envoyee = 1,
  Payee = 2,
  EnRetard = 3,
  Annulee = 4
}

export interface FactureVente {
  id: string;
  numero: string;
  montant: number;
  date: Date;
  dateEcheance?: Date;
  statut: StatutFacture;
  dateEnvoi?: Date;
  datePaiement?: Date;
  notesInternes?: string;
  matriculeFiscaleSociete: string;
  societeId: string;
  societe?: Societe;
  utilisateurCreationId?: string;
  utilisateurCreation?: Applicationuser;
  dateCreation: Date;
  dateModification?: Date;
  clientId: string;
  client?: Client;
}