import { FactureAchat } from "./facture-achat";
import { Societe } from "./societe";

export enum StatutFournisseur {
  Actif = 0,
  Inactif = 1,
  Suspendu = 2
}

export interface Fournisseur {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  adresse: string;
  statut: StatutFournisseur;
  dateCreation: Date;
  societeId: string;
  societe?: Societe;
  facturesAchat?: FactureAchat[];
}