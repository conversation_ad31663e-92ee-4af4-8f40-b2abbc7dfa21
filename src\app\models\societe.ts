import { Applicationuser } from "./applicationuser";
import { Client } from "./client";
import { Document } from "./document";
import { FactureAchat } from "./facture-achat";
import { FactureVente } from "./facture-vente";
import { Fournisseur } from "./fournisseur";
import { RetenueSource } from "./retenue-source";

export interface Societe {
  id: string;
  nom: string;
  adresse: string;
  matriculeFiscale: string;
  email?: string;
  telephone?: string;
  logo?: string;
  signature?: string;
  cachet?: string;
  utilisateurs?: Applicationuser[];
  clients?: Client[];
  fournisseurs?: Fournisseur[];
  facturesAchat?: FactureAchat[];
  facturesVente?: FactureVente[];
  documents?: Document[];
  retenuesSource?: RetenueSource[];
}