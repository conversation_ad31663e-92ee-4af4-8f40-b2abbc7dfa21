import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { UtilisateurCreateDto, UtilisateurDto } from '../models/applicationuser';

@Injectable({
  providedIn: 'root'
})
export class UtilisateurService {
  private apiUrl = 'http://localhost:5251/api/utilisateurs';

  constructor(private http: HttpClient) {}

  getUtilisateurs(): Observable<UtilisateurDto[]> {
    return this.http.get<UtilisateurDto[]>(this.apiUrl);
  }

  getUtilisateur(id: string): Observable<UtilisateurDto> {
    return this.http.get<UtilisateurDto>(`${this.apiUrl}/${id}`);
  }

  postUtilisateur(utilisateur: UtilisateurCreateDto): Observable<UtilisateurDto> {
    return this.http.post<UtilisateurDto>(this.apiUrl, utilisateur);
  }

  putUtilisateur(id: string, utilisateur: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, utilisateur);
  }

  deleteUtilisateur(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
}