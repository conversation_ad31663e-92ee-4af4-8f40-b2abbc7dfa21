import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { RetenueSource } from '../models/retenue-source';



@Injectable({
  providedIn: 'root'
})
export class RetenueSourceService {
  private apiUrl = 'http://localhost:5251/api/RetenueSource';

  constructor(private http: HttpClient) {}

  getAll(): Observable<RetenueSource[]> {
    return this.http.get<RetenueSource[]>(this.apiUrl);
  }

  getById(id: string): Observable<RetenueSource> {
    return this.http.get<RetenueSource>(`${this.apiUrl}/${id}`);
  }

  update(id: string, retenue: RetenueSource): Observable<RetenueSource> {
    return this.http.put<RetenueSource>(`${this.apiUrl}/${id}`, retenue);
  }

  delete(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }

  createFromUpload(formData: FormData): Observable<RetenueSource> {
    return this.http.post<RetenueSource>(`${this.apiUrl}/upload`, formData);
  }

  downloadRetenueFile(id: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/${id}/download-file`, { responseType: 'blob' });
  }
}