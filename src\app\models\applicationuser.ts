import { Societe } from "./societe";

export interface Applicationuser {
  id: string;
  nom: string;
  email: string;
  userName: string;
  phoneNumber?: string;
  societeId: string;
  societe?: Societe;
}

export interface UtilisateurDto {
  id: string;
  nom: string;
  email: string;
  societeId: string;
  societeNom: string;
  role: string;
}

export interface UtilisateurCreateDto {
  nom: string;
  email: string;
  motDePasse: string;
  role: string;
}