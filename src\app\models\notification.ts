import { Applicationuser } from "./applicationuser";
import { Societe } from "./societe";

export enum TypeNotification {
  FactureCreee = 0,
  FactureEnvoyee = 1,
  FacturePayee = 2,
  FactureEnRetard = 3,
  DocumentDemande = 4,
  DocumentApprouve = 5,
  DocumentRejete = 6,
  UtilisateurCree = 7,
  RetenueSourceAjoutee = 8,
  Systeme = 9
}

export enum StatutNotification {
  NonLue = 0,
  Lue = 1,
  Archivee = 2
}

export interface Notification {
  id: string;
  titre: string;
  message: string;
  type: TypeNotification;
  statut: StatutNotification;
  dateCreation: Date;
  dateLecture?: Date;
  utilisateurId: string;
  utilisateur?: Applicationuser;
  societeId: string;
  societe?: Societe;
  entityId?: string;
  entityType?: string;
}